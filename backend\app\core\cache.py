"""
Simple in-memory cache for performance optimization.
"""
import time
from typing import Any, Dict, Optional, Tuple
from uuid import UUID
import logging

logger = logging.getLogger(__name__)

class SimpleCache:
    """Simple in-memory cache with TTL support."""
    
    def __init__(self, default_ttl: int = 300):  # 5 minutes default
        self._cache: Dict[str, Tuple[Any, float]] = {}
        self._default_ttl = default_ttl
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache if not expired."""
        if key not in self._cache:
            return None
        
        value, expiry = self._cache[key]
        if time.time() > expiry:
            # Expired, remove from cache
            del self._cache[key]
            return None
        
        return value
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set value in cache with TTL."""
        if ttl is None:
            ttl = self._default_ttl
        
        expiry = time.time() + ttl
        self._cache[key] = (value, expiry)
    
    def delete(self, key: str) -> None:
        """Delete key from cache."""
        if key in self._cache:
            del self._cache[key]
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
    
    def cleanup_expired(self) -> None:
        """Remove expired entries from cache."""
        current_time = time.time()
        expired_keys = [
            key for key, (_, expiry) in self._cache.items()
            if current_time > expiry
        ]
        
        for key in expired_keys:
            del self._cache[key]
        
        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

# Global cache instance
_cache_instance = SimpleCache()

def get_cache() -> SimpleCache:
    """Get the global cache instance."""
    return _cache_instance

def cache_key_procesos_empresa(empresa_id: UUID) -> str:
    """Generate cache key for empresa processes."""
    return f"procesos_empresa:{empresa_id}"

def cache_key_contadores_empresa(empresa_id: UUID) -> str:
    """Generate cache key for empresa process counters."""
    return f"contadores_empresa:{empresa_id}"

def cache_key_proceso_detalle(proceso_id: UUID) -> str:
    """Generate cache key for process details."""
    return f"proceso_detalle:{proceso_id}"

def invalidate_empresa_cache(empresa_id: UUID) -> None:
    """Invalidate all cache entries for an empresa."""
    cache = get_cache()
    cache.delete(cache_key_procesos_empresa(empresa_id))
    cache.delete(cache_key_contadores_empresa(empresa_id))
    logger.debug(f"Invalidated cache for empresa {empresa_id}")

def invalidate_proceso_cache(proceso_id: UUID, empresa_id: UUID) -> None:
    """Invalidate cache entries for a specific process and its empresa."""
    cache = get_cache()
    cache.delete(cache_key_proceso_detalle(proceso_id))
    invalidate_empresa_cache(empresa_id)
    logger.debug(f"Invalidated cache for proceso {proceso_id}")

def cache_key_personas_batch(persona_ids: list) -> str:
    """Generate cache key for batch persona queries."""
    # Sort IDs to ensure consistent cache keys
    sorted_ids = sorted(str(pid) for pid in persona_ids)
    ids_hash = hash(tuple(sorted_ids))
    return f"personas_batch:{ids_hash}"

def cache_key_responsables_batch(tarea_ids: list) -> str:
    """Generate cache key for batch responsables queries."""
    # Sort IDs to ensure consistent cache keys
    sorted_ids = sorted(str(tid) for tid in tarea_ids)
    ids_hash = hash(tuple(sorted_ids))
    return f"responsables_batch:{ids_hash}"

def cache_key_departamentos_batch(dept_ids: list) -> str:
    """Generate cache key for batch departamentos queries."""
    # Sort IDs to ensure consistent cache keys
    sorted_ids = sorted(str(did) for did in dept_ids)
    ids_hash = hash(tuple(sorted_ids))
    return f"departamentos_batch:{ids_hash}"
