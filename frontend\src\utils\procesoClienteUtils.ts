/**
 * Utilidades para el manejo de procesos de cliente
 * Funciones de formateo, conversión y validación reutilizables
 */

import {
  VALOR_NEGOCIO_COLORS,
  PRIORIDAD_AUTOMATIZACION_COLORS,
  ESTADO_ANALISIS_COLORS,
} from '../types/proceso_cliente';

// Constantes para strings reutilizables
export const PROCESO_CLIENTE_LABELS = {
  SIN_ASIGNAR: 'Sin asignar',
  SIN_RESPONSABLES: 'Sin responsables',
  SELECCIONA_PROCESO: 'Selecciona un proceso para ver sus detalles',
  NO_TAREAS_ENCONTRADAS: 'No se encontraron tareas',
  BUSCAR_TAREAS: 'Buscar tareas...',
  ES_CUELLO_BOTELLA: 'Es cuello de botella',
  ES_REPETITIVO: 'Es repetitivo',
  ES_MANUAL: 'Es manual',
} as const;

// Tipos para las funciones de utilidad
type BadgeType = 'valor' | 'prioridad' | 'estado';
type ColorMappings = {
  valor: typeof VALOR_NEGOCIO_COLORS;
  prioridad: typeof PRIORIDAD_AUTOMATIZACION_COLORS;
  estado: typeof ESTADO_ANALISIS_COLORS;
};

/**
 * Formatea tiempo en horas a una representación legible
 * @param horas - Número de horas a formatear
 * @returns String formateado (ej: "2.5h", "30min", "0h")
 */
export const formatTiempo = (horas: number): string => {
  if (horas === 0) return '0h';
  if (horas < 1) return `${Math.round(horas * 60)}min`;
  return `${horas.toFixed(1)}h`;
};

/**
 * Convierte un valor a número entero de forma segura
 * @param value - Valor a convertir (puede ser string, number, boolean, null o undefined)
 * @returns Número entero o null si la conversión falla
 */
export const safeParseInt = (value: string | number | boolean | null | undefined): number | null => {
  if (value === null || value === undefined || value === '' || typeof value === 'boolean') return null;
  const parsed = parseInt(String(value));
  return isNaN(parsed) ? null : parsed;
};

/**
 * Obtiene la clase CSS para badges basada en el tipo y valor
 * @param value - Valor a evaluar
 * @param type - Tipo de badge ('valor', 'prioridad', 'estado')
 * @returns Clase CSS para el badge
 */
export const getBadgeColor = (
  value: string | number | boolean | null, 
  type: BadgeType
): string => {
  if (!value || typeof value !== 'string') {
    return 'bg-gray-100 text-gray-800';
  }

  const colorMappings: ColorMappings = {
    valor: VALOR_NEGOCIO_COLORS,
    prioridad: PRIORIDAD_AUTOMATIZACION_COLORS,
    estado: ESTADO_ANALISIS_COLORS,
  };

  const mapping = colorMappings[type];
  return mapping[value as keyof typeof mapping] || 'bg-gray-100 text-gray-800';
};

/**
 * Valida si un valor es válido para actualización
 * @param value - Valor a validar
 * @returns true si el valor es válido
 */
export const isValidUpdateValue = (value: unknown): boolean => {
  return value !== undefined && value !== null && value !== '';
};

/**
 * Crea un objeto de actualización limpio removiendo valores undefined/null
 * @param updates - Objeto con las actualizaciones
 * @returns Objeto limpio sin valores undefined/null
 */
export const createCleanUpdateObject = <T extends Record<string, unknown>>(
  updates: T
): Partial<T> => {
  const cleanUpdates: Partial<T> = {};
  
  Object.entries(updates).forEach(([key, value]) => {
    if (isValidUpdateValue(value)) {
      cleanUpdates[key as keyof T] = value as T[keyof T];
    }
  });
  
  return cleanUpdates;
};

/**
 * Maneja errores de actualización de forma consistente
 * @param error - Error capturado
 * @param context - Contexto donde ocurrió el error
 */
export const handleUpdateError = (error: unknown, context: string): void => {
  console.error(`Error en ${context}:`, error);
  
  // En un entorno de producción, aquí se podría enviar el error a un servicio de logging
  // como Sentry, LogRocket, etc.
  
  // Por ahora, solo logueamos el error. En el futuro se podría mostrar un toast
  // o notificación al usuario
};

/**
 * Debounce function para optimizar llamadas frecuentes
 * @param func - Función a ejecutar
 * @param delay - Retraso en milisegundos
 * @returns Función debounced
 */
export const debounce = <T extends (...args: unknown[]) => void>(
  func: T,
  delay: number
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout;
  
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => func(...args), delay);
  };
};
